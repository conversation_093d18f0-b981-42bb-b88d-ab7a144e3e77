package com.yuelan.plugins.oss.aliyun;


import com.yuelan.plugins.oss.core.OssConfig;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * <p> 阿里云配置 </p>
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
public class AliyunOssConfig extends OssConfig {

    private String accessKeyId;

    private String accessKeySecret;

    private String endPoint;


}