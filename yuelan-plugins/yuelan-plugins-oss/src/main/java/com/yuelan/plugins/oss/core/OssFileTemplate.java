package com.yuelan.plugins.oss.core;


import cn.hutool.core.io.FileUtil;
import com.aliyun.oss.model.CannedAccessControlList;
import com.yuelan.plugins.oss.core.aspect.OssHostAspect;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

public abstract class OssFileTemplate implements InitializingBean, DisposableBean {

    protected final OssConfig ossConfig;

    public OssFileTemplate(OssConfig ossConfig) {
        this.ossConfig = ossConfig;
        OssHostAspect.putOssConfig(ossConfig);
    }

    public abstract String uploadFile(String bucketName, String objectKey, File file) throws IOException;

    public abstract String uploadFile(String bucketName, String objectKey, File file, CannedAccessControlList acl) throws IOException;


    public String uploadFile(String bucketName, File file, String folder) throws IOException {
        //获取后缀
        String suffix = FileUtil.getSuffix(file);
        // 获取唯一的文件名
        String fileName = OssUtils.getUniqueName(suffix);
        String objectKey = OssUtils.buildObjectKey(fileName, folder);
        return uploadFile(bucketName, objectKey, file);
    }

    public String uploadFile(File file, String folder) throws IOException {
        return uploadFile(ossConfig.getBucket(), file, folder);
    }

    public String uploadFile(File file) throws IOException {
        return uploadFile(file, ossConfig.getFolder());
    }

    public String uploadFile(File file, String folder, CannedAccessControlList acl) throws IOException {
        String suffix = FileUtil.getSuffix(file);
        String fileName = OssUtils.getUniqueName(suffix);
        String objectKey = OssUtils.buildObjectKey(fileName, folder);
        return uploadFile(ossConfig.getBucket(), objectKey, file, acl);
    }

    /**
     * 文件上传
     *
     * @param is       文件流
     * @param fileName 文件名称
     * @param mimeType 媒体类型
     * @param folder   目标文件夹
     * @return 上传后的文件路径
     *
     * @throws IOException
     */
    public String uploadFile(InputStream is, String fileName, String mimeType, String folder) throws IOException {
        String objectKey = OssUtils.buildObjectKey(fileName, folder);
        return uploadFile(ossConfig.getBucket(), objectKey, is, fileName, mimeType);
    }

    public String getFullUrl(String filePath) {
        return OssUtils.getFileUrl(ossConfig.getHost(), filePath);
    }


    public abstract String uploadFile(String bucketName, String objectKey, InputStream is, String fileName, String mimeType) throws IOException;

    /**
     * 文件上传
     *
     * @param is       文件流
     * @param fileName 文件名称
     * @param mimeType 媒体类型
     * @return 上传后的文件路径
     *
     * @throws IOException
     */
    public String uploadFile(InputStream is, String fileName, String mimeType) throws IOException {
        return uploadFile(is, fileName, mimeType, ossConfig.getFolder());
    }


    /**
     * 文件上传
     *
     * @param file 文件
     * @return 上传后的文件路径
     *
     * @throws IOException
     */
    public String uploadFile(MultipartFile file) throws IOException {
        return uploadFile(file, ossConfig.getFolder());
    }

    /**
     * 文件上传
     *
     * @param file   文件
     * @param folder 目标文件夹
     * @return 上传后的文件路径
     *
     * @throws IOException
     */
    public String uploadFile(MultipartFile file, String folder) throws IOException {
        // 获取文件的mime type，校验文件类型是否合法
        InputStream is = file.getInputStream();
        String mimeType = file.getContentType();
        String suffix = OssUtils.getSuffix(file.getOriginalFilename());
        // 获取唯一的文件名
        String fileName = OssUtils.getUniqueName(suffix);
        return uploadFile(is, fileName, mimeType, folder);
    }

    /**
     * @param bucketName 存储桶
     * @param objectKey  文件名称（bucket下的全路径名）
     * @param directory  下载目录
     * @return 下载后的文件地址（绝对路径）
     */
    public String downloadString(String bucketName, String objectKey, String directory) {
        File file = downloadFile(bucketName, objectKey, directory);
        if (Objects.isNull(file)) {
            return null;
        }
        return file.getAbsolutePath();
    }

    /**
     * 下载文件到指定目录
     *
     * @param objectKey 文件名称（bucket下的全路径名）
     * @param directory 下载目录
     * @return 下载后的文件地址（绝对路径）
     */
    public String downloadString(String objectKey, String directory) {
        return downloadString(ossConfig.getBucket(), objectKey, directory);
    }

    /**
     * 下载文件到临时目录
     *
     * @param objectKey 文件名称（bucket下的全路径名）
     * @return 下载后的文件地址（绝对路径）
     */
    public String downloadString(String objectKey) {
        return downloadString(objectKey, FileUtil.getTmpDirPath());
    }

    /**
     * @param bucketName 存储桶
     * @param objectKey  文件名称（bucket下的全路径名）
     * @param directory  下载目录
     * @return 下载后的文件
     */
    public abstract File downloadFile(String bucketName, String objectKey, String directory);

    /**
     * 下载文件到指定目录
     *
     * @param objectKey 文件名称（bucket下的全路径名）
     * @param directory 下载目录
     * @return 下载后的文件
     */
    public File downloadFile(String objectKey, String directory) {
        return downloadFile(ossConfig.getBucket(), objectKey, directory);
    }

    /**
     * 下载文件到临时目录
     *
     * @param objectKey 文件名称（bucket下的全路径名）
     * @return 下载后的文件
     */
    public File downloadFile(String objectKey) {
        return downloadFile(objectKey, FileUtil.getTmpDirPath());
    }

    /**
     * 获取文件域名
     */
    public String getFileHost() {
        return ossConfig.getHost();
    }

    /**
     * 获取文件全路径
     *
     * @param objectKey 文件名称（bucket下的全路径名）
     */
    public String getFileUrl(String objectKey) {
        return OssUtils.getFileUrl(ossConfig.getHost(), objectKey);
    }

    /**
     * 获取可过期的文件地址
     *
     * @param bucketName 存储桶
     * @param objectKey  文件名称（bucket下的全路径名）
     * @param time       过期时间 单位：秒
     * @param fileName   指定文件名字
     * @return 指定过期时间的文件地址
     */
    public abstract String getAclUrl(String bucketName, String objectKey, String fileName, int time);

    /**
     * 获取可过期的文件地址
     *
     * @param objectKey 文件名称（bucket下的全路径名）
     * @param time      过期时间 单位：秒
     * @param fileName  指定文件名字如果为null 则用原文件名字
     * @return 指定过期时间的文件地址
     */
    public String getAclUrl(String objectKey, String fileName, int time) {
        return getAclUrl(ossConfig.getBucket(), objectKey, fileName, time);
    }

    /**
     * 获取可过期的文件地址，配置中的时间
     *
     * @param objectKey 文件名称（bucket下的全路径名）
     * @return 指定过期时间的文件地址
     */
    public String getAclUrl(String objectKey) {
        return getAclUrl(objectKey, null, ossConfig.getUrlAcl());
    }
}
