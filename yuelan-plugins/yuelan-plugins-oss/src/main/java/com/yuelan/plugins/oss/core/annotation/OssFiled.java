package com.yuelan.plugins.oss.core.annotation;


import com.yuelan.plugins.oss.core.aspect.HostReplaceEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface OssFiled {

    HostReplaceEnum value() default HostReplaceEnum.JOIN_REPLACE;
}
