package com.yuelan.plugins.oss.core;


import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p> 对象存储 </p>
 */
@Data
public class OssConfig {
    /**
     * 存储名称
     */
    private String name;
    /**
     * 存储桶
     */
    private String bucket;
    /**
     * 目标文件夹,例如: static/js
     */
    private String folder;
    /**
     * 访问域名
     */
    private String host;
    /**
     * 文件链接可以访问的时间：单位秒，默认1小时
     */
    private int urlAcl = 3600;
    /**
     * 需要过滤的域名
     */
    private List<String> domain = new ArrayList<>();

}