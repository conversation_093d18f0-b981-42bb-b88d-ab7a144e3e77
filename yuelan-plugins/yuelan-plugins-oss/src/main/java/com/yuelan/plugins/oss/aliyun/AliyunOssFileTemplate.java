package com.yuelan.plugins.oss.aliyun;


import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.model.*;
import com.yuelan.plugins.oss.core.OssFileTemplate;
import com.yuelan.plugins.oss.core.OssUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Date;
import java.util.Objects;

@Slf4j
public class AliyunOssFileTemplate extends OssFileTemplate {

    private final OSSClient ossClient;
    private final AliyunOssConfig aliyunOssConfig;

    public AliyunOssFileTemplate(AliyunOssConfig ossConfig) {
        super(ossConfig);
        this.aliyunOssConfig = ossConfig;
        CredentialsProvider credentialsProvider = new DefaultCredentialProvider(aliyunOssConfig.getAccessKeyId(),
                aliyunOssConfig.getAccessKeySecret());
        this.ossClient = new OSSClient(aliyunOssConfig.getEndPoint(), credentialsProvider, null);
    }

    @Override
    public void destroy() throws Exception {
        if (this.ossClient == null) {
            return;
        }
        this.ossClient.shutdown();
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        log.info(">>>>>>>>>>> success aliyun oss ");
    }

    public String uploadFile(String bucketName, String objectKey, File file, ObjectMetadata metadata) throws IOException {
        ossClient.putObject(bucketName, objectKey, file, metadata);
        return objectKey;
    }

    public String uploadFile(String bucketName, String objectKey, InputStream is, ObjectMetadata metadata) throws IOException {
        ossClient.putObject(bucketName, objectKey, is, metadata);
        return objectKey;
    }

    @Override
    public String uploadFile(String bucketName, String objectKey, InputStream is, String fileName, String mimeType) throws IOException {
        ObjectMetadata objectMeta = new ObjectMetadata();
        objectMeta.setContentLength(is.available());
        objectMeta.setContentType(mimeType);
        objectMeta.setContentDisposition("inline; filename=" + fileName);
        return uploadFile(bucketName, objectKey, is, objectMeta);
    }

    @Override
    public String uploadFile(String bucketName, String objectKey, File file) throws IOException {
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectKey, file);
        ossClient.putObject(putObjectRequest);
        return objectKey;
    }

    @Override
    public String uploadFile(String bucketName, String objectKey, File file, CannedAccessControlList acl) throws IOException {
        PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, objectKey, file);
        ossClient.putObject(putObjectRequest);
        ossClient.setObjectAcl(bucketName, objectKey, acl);
        return objectKey;
    }

    @Override
    public File downloadFile(String bucketName, String objectKey, String directory) {
        if (StrUtil.isEmpty(objectKey)) {
            return null;
        }
        String filename = StrUtil.subAfter(objectKey, StrUtil.C_SLASH, true);
        File file = FileUtil.file(directory, filename);
        ossClient.getObject(new GetObjectRequest(bucketName, objectKey), file);
        return file;
    }

    @Override
    public String getAclUrl(String bucketName, String objectKey, String fileName, int time) {
        if (StrUtil.isEmpty(objectKey)) {
            return null;
        }
        // 设置 acl 过期时间
        Date expiration = DateUtil.offsetSecond(DateUtil.date(), time);
        GeneratePresignedUrlRequest request = new GeneratePresignedUrlRequest(bucketName, objectKey);
        request.setExpiration(expiration);
        if (Objects.nonNull(fileName)) {
            ResponseHeaderOverrides res = new ResponseHeaderOverrides();
            res.setContentType("application/octet-stream");
            res.setContentDisposition("attachment; filename=" + fileName);
            request.setResponseHeaders(res);
        }
        // 获取url的权限
        URL url = ossClient.generatePresignedUrl(request);
        return OssUtils.replaceHost(url.toString(), ossConfig.getHost(), ossConfig.getDomain());
    }

}