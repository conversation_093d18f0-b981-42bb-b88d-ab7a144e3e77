package com.yuelan.plugins.oss.aliyun;


import com.aliyun.oss.OSSClient;
import com.yuelan.plugins.oss.core.aspect.OssHostAspect;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Lazy;

@Lazy(false)
@AutoConfiguration
@ConditionalOnClass(OSSClient.class)
public class AliyunOssRegister {

    @Bean
    @ConfigurationProperties(prefix = "oss.aliyun")
    public AliyunOssConfig aliyunOssConfig() {
        return new AliyunOssConfig();
    }

    @Bean
    public AliyunOssFileTemplate aliyunOssFileTemplate(AliyunOssConfig config) {
        return new AliyunOssFileTemplate(config);
    }

    @Bean
    public OssHostAspect ossHostAspect() {
        return new OssHostAspect();
    }
}
