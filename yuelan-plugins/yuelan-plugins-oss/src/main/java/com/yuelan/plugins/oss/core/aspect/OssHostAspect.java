package com.yuelan.plugins.oss.core.aspect;


import cn.hutool.core.annotation.AnnotationUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.yuelan.plugins.oss.core.OssConfig;
import com.yuelan.plugins.oss.core.OssUtils;
import com.yuelan.plugins.oss.core.annotation.OssFiled;
import com.yuelan.plugins.oss.core.annotation.OssHostReplace;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Aspect
public class OssHostAspect {

    public static Map<String, OssConfig> ossConfigMap = new LinkedHashMap<String, OssConfig>();

    public static void putOssConfig(OssConfig ossConfig) {
        ossConfigMap.put(ossConfig.getBucket(), ossConfig);
    }

    @Pointcut("@annotation(com.yuelan.plugins.oss.core.annotation.OssHostReplace)")
    public void hostPointcut() {
    }

    @Around("hostPointcut()")
    public Object aspect(final ProceedingJoinPoint joinPoint) throws Exception, Throwable {
        // 获取对应的 Method 处理函数
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        OssHostReplace ossHostReplace = AnnotationUtil.getAnnotation(method, OssHostReplace.class);
        if (Objects.isNull(ossHostReplace)) {
            return joinPoint.proceed();
        }

        OssConfig ossConfig = getOssConfig(ossHostReplace);
        if (Objects.isNull(ossConfig)) {
            return joinPoint.proceed();
        }
        Object[] args = joinPoint.getArgs();
        removeHost(args, ossConfig);

        Object proceed = joinPoint.proceed();

        Object[] results = {proceed};

        replaceHost(results, ossConfig);

        return proceed;
    }

    private OssConfig getOssConfig(OssHostReplace ossHostReplace) {
        String name = ossHostReplace.name();
        if (StrUtil.isNotEmpty(name)) {
            return ossConfigMap.get(name);
        }


        return CollectionUtil.getFirst(ossConfigMap.values());
    }

    /**
     * 去除域名
     */
    private void removeHost(Object[] args, OssConfig ossConfig) throws Exception {

        if (args == null || args.length <= 0) return;

        for (Object arg : args) {

            if (arg instanceof List) {
                listDeserializer((List) arg, ossConfig);
            } else {
                // 其他默认dto
                beanDeserializer(arg, ossConfig);
            }
        }
    }

    private void listDeserializer(List args, OssConfig ossConfig) throws Exception {
        for (Object arg : args) {
            beanDeserializer(arg, ossConfig);
        }
    }

    private void beanDeserializer(Object arg, OssConfig ossConfig) throws Exception {
        Class clazz = arg.getClass();
//        Field[] superField = clazz.getSuperclass().getDeclaredFields();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            Object o = field.get(arg);
            if (o instanceof String) {
                OssFiled ossFiled = field.getAnnotation(OssFiled.class);
                if (ossFiled == null) {
                    continue;
                }
                if (Objects.equals(HostReplaceEnum.NONE, ossFiled.value())) {
                    continue;
                }
                String url = removeHost((String) o, ossConfig);
                field.set(arg, url);
            } else if (o instanceof List) {
                listDeserializer((List) o, ossConfig);
            } else if (!checkType(o)) {
                beanDeserializer(o, ossConfig);
            }
        }
    }


    private void replaceHost(Object[] args, OssConfig ossConfig) throws Exception {

        if (args == null || args.length <= 0) return;

        for (Object arg : args) {

            if (arg instanceof List) {
                listSerializer((List) arg, ossConfig);
            } else {
                // 其他默认dto
                beanSerializer(arg, ossConfig);
            }
        }
    }

    private void listSerializer(List args, OssConfig ossConfig) throws Exception {
        for (Object arg : args) {
            beanSerializer(arg, ossConfig);
        }
    }

    private void beanSerializer(Object arg, OssConfig ossConfig) throws Exception {
        Class clazz = arg.getClass();
        for (Field field : clazz.getDeclaredFields()) {
            field.setAccessible(true);
            Object o = field.get(arg);
            if (o instanceof String) {
                OssFiled ossFiled = field.getAnnotation(OssFiled.class);
                if (ossFiled == null) {
                    continue;
                }
                if (Objects.equals(HostReplaceEnum.JOIN_REPLACE, ossFiled.value())) {
                    String url = replaceHost((String) o, ossConfig);
                    field.set(arg, url);
                }
            } else if (o instanceof List) {
                listSerializer((List) o, ossConfig);
            } else if (!checkType(o)) {
                beanSerializer(o, ossConfig);
            }
        }
    }

    private String removeHost(String url, OssConfig ossConfig) {
        return OssUtils.removeHost(url, ossConfig.getHost(), ossConfig.getDomain());
    }

    private String replaceHost(String url, OssConfig ossConfig) {
        return OssUtils.replaceHost(url, ossConfig.getHost(), ossConfig.getDomain());
    }

    private boolean checkType(Object o) {
        return o == null || o instanceof Double || o instanceof Integer || o instanceof Long || o instanceof Boolean;
    }
}
