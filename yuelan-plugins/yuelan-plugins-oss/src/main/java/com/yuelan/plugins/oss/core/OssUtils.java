package com.yuelan.plugins.oss.core;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;

import java.util.List;

public class OssUtils {

    /**
     * @param suffix 文件后缀
     * @return 文件mime type
     */
    public static String getMimeType(String suffix) {
        switch (suffix) {
            case "xls":
            case "xlsx":
                return "application/vnd.ms-excel";
            case "pdf":
                return "application/pdf";
            case "doc":
            case "docx":
            case "dot":
                return "application/msword";
            case "zip":
                return "application/zip";
            case "gif":
                return "image/gif";
            case "png":
                return "image/png";
            case "svg":
                return "image/svg+xml";
            case "tif":
            case "tiff":
                return "image/tiff";
            case "ico":
                return "image/x-icon";
            case "fax":
                return "image/fax";
            default:
                return "image/jpg";
        }
    }

    /**
     * 生成文件全路径名
     * ${folder}/2020/05/15/${fileName}
     */
    public static String buildObjectKey(String fileName, String folder) {
        String objectKey = DateUtil.format(new DateTime(), "yyyy/MM/dd/") + fileName;
        if (StrUtil.isBlank(folder)) {
            return objectKey;
        }
        return folder + "/" + objectKey;
    }

    /**
     * 获取文件后缀
     * 1.pdf=》pdf
     *
     * @param originalFilename 原文件名称
     * @return 文件后缀
     */
    public static String getSuffix(String originalFilename) {
        if (StrUtil.isEmpty(originalFilename) || !originalFilename.contains(StrUtil.DOT)) {
            return StrUtil.EMPTY;
        }
        return originalFilename.substring(originalFilename.lastIndexOf(StrUtil.DOT) + 1).trim().toLowerCase();
    }

    /**
     * 生成唯一文件名
     *
     * @param suffix 后缀名称
     * @return 唯一的文件名称
     */
    public static String getUniqueName(String suffix) {
        return IdUtil.fastSimpleUUID() + StrUtil.DOT + suffix;
    }

    /**
     * 获取文件全路径
     *
     * @param host      访问域名
     * @param objectKey 文件名称（bucket下的全路径名）
     */
    public static String getFileUrl(String host, String objectKey) {
        if (StrUtil.isEmpty(objectKey)) {
            return "";
        }
        if (StrUtil.isEmpty(host)) {
            return objectKey;
        }
        if (StrUtil.startWith(objectKey, "http")) {
            return objectKey;
        } else if (StrUtil.startWith(objectKey, "//")) {
            return objectKey;
        }
        return host + StrUtil.addPrefixIfNot(objectKey, "/");
    }

    /**
     * 删除文件地址域名
     *
     * @param url        文件地址
     * @param targetHost 目标域名
     * @param filterHost 待过滤的域名
     * @return objectKey
     */
    public static String removeHost(final String url, String targetHost, List<String> filterHost) {
        if (StrUtil.isBlank(url)) {
            return "";
        }
        String result = null;
        if (url.startsWith(targetHost)) {
            return url.substring(targetHost.length());
        } else if (url.startsWith("https://")) {
            result = url.substring(8);
        } else if (url.startsWith("http://")) {
            result = url.substring(7);
        } else if (url.startsWith("//")) {
            result = url.substring(2);
        }
        if (StrUtil.isEmpty(result)) {
            return "";
        }
        for (String domain : filterHost) {
            if (result.startsWith(domain)) {
                result = result.substring(domain.length());
                break;
            }
        }
        return StrUtil.removePrefix(result, "/");
    }

    /**
     * 替换文件域名
     *
     * @param url        文件地址
     * @param targetHost 目标域名
     * @param filterHost 待过滤的域名
     * @return 远程文件地址
     */
    public static String replaceHost(final String url, String targetHost, List<String> filterHost) {
        if (StrUtil.isBlank(url)) {
            return "";
        }
        if (url.startsWith(targetHost)) {
            return url;
        }
        String result = null;
        if (url.startsWith("https://")) {
            result = url.substring(8);
        } else if (url.startsWith("http://")) {
            result = url.substring(7);
        } else if (url.startsWith("//")) {
            result = url.substring(2);
        }
        if (StrUtil.isEmpty(result)) {
            return "";
        }
        for (String domain : filterHost) {
            if (result.startsWith(domain)) {
                result = result.substring(domain.length());
                break;
            }
        }
        return targetHost + StrUtil.addPrefixIfNot(result, "/");
    }

}
