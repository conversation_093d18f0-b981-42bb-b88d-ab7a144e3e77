package com.yuelan.result.enums;


import com.yuelan.result.able.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <p> 布尔值 </p>
 */
@Getter
@AllArgsConstructor
public enum YesOrNoEnum implements IEnum<YesOrNoEnum> {

    NO(0, "否"),
    YES(1, "是");
    //
    private Integer code;
    private String desc;

    /**
     * code!=1都认为是false
     */
    public static boolean isYes(Integer code) {
        return Objects.equals(YesOrNoEnum.YES.getCode(), code);
    }

    public static YesOrNoEnum getEnum(boolean value) {
        return value ? YesOrNoEnum.YES : YesOrNoEnum.NO;
    }

    public static Integer getCode(boolean value) {
        return value ? YesOrNoEnum.YES.code : YesOrNoEnum.NO.code;
    }

    @Override
    public YesOrNoEnum getDefault() {
        return NO;
    }

    public static YesOrNoEnum of(Integer code) {
        for (YesOrNoEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
