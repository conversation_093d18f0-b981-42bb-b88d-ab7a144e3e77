package com.yuelan.result.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;


/**
 * <p> ID分页返回 </p>
 */
@Data
@Accessors(chain = true)
@RequiredArgsConstructor(staticName = "of", access = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class IDPageData<T> implements Serializable {

    private static final long serialVersionUID = -4257092979237049883L;
    /**
     * 结果集
     */
    private List<T> list;
    /**
     * 当前页第一条的ID
     */
    private Object firstId;
    /**
     * 当前页最后一条的ID
     */
    private Object lastId;
    /**
     * 开始ID
     */
    private Object startId;
    /**
     * 结束ID
     */
    private Object endId;
    /**
     * 扩展信息
     */
    private Map<String, Object> extra;

    /**
     * 正常返回
     *
     * @param data 数据内容
     * @param <T>  返回数据的类型
     * @return 包装后的返回
     */
    public static <T> IDPageData<T> create(List<T> data) {
        return create(data, null, null);
    }

    public static <T> IDPageData<T> create(List<T> data, Object firstId, Object lastId) {
        return create(data, firstId, lastId, null, null);
    }

    public static <T> IDPageData<T> create(List<T> data, Object firstId, Object lastId, Object startId, Object endId) {
        return create(data, firstId, lastId, startId, endId);
    }

    public static <T> IDPageData<T> create(List<T> data, Object firstId, Object lastId, Object startId, Object endId,
                                           Map extra) {
        return IDPageData.<T>of()
                .setList(data)
                .setStartId(startId)
                .setEndId(endId)
                .setFirstId(firstId)
                .setLastId(lastId)
                .setExtra(extra);
    }
}