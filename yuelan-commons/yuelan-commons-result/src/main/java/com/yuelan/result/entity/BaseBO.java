package com.yuelan.result.entity;

import com.yuelan.result.enums.YesOrNoEnum;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class BaseBO {

    /**
     * 创建时间
     */
    protected Date createTime;
    /**
     * 更新时间
     */
    protected Date updateTime;
    /**
     * 删除字段
     */
    protected boolean deleted;

    public void from(BaseDO baseDO) {
        if (Objects.isNull(baseDO)) {
            return;
        }
        this.deleted = YesOrNoEnum.isYes(baseDO.getDeleted());
        this.createTime = baseDO.getCreateTime();
        this.updateTime = baseDO.getUpdateTime();
    }
}