package com.yuelan.result.entity;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <p> 分页请求对象 </p>
 */
@NoArgsConstructor
public class PageRequest implements java.io.Serializable {

    private static final long serialVersionUID = -3249240106177676069L;
    /**
     * 页码，从1开始
     */
    private long page = 1L;
    /**
     * 每页显示数量
     */
    private int size = 10;

    /**
     * order by
     */
    @Getter
    @Setter
    private String orderBy;

    /**
     * 是否升序
     */
    @Getter
    @Setter
    private boolean asc = true;


    private PageRequest(long page, int size) {
        if (page < 1) {
            throw new IllegalArgumentException("Page index must not be less than one!");
        }
        if (size < 1) {
            throw new IllegalArgumentException("Page size must not be less than one!");
        }

        this.page = page;
        this.size = size;
    }

    public static PageRequest of(long page, int size) {
        return new PageRequest(page, size);
    }

    /**
     * 每页显示数量
     */
    public int getSize() {
        return size;
    }

    /**
     * 设置每页显示数量 如果小于1 或者大于100则抛出异常
     * @param size 每页显示数量
     */
    public void setSize(int size) {
        if (size < 1) {
            throw new IllegalArgumentException("Page size must not be less than one!");
        }
        if (size > 1000) {
            throw new IllegalArgumentException("Page size must not greater than 100!");
        }
        this.size = size;
    }

    /**
     * 强制设置每页显示数量
     */
    public void forceSetSize(int size) {
        if (size < 1) {
            throw new IllegalArgumentException("Page size must not be less than one!");
        }
        this.size = size;
    }

    /**
     * 页码
     */
    public long getPage() {
        return page;
    }

    public void setPage(long page) {
        if (page < 1) {
            throw new IllegalArgumentException("Page index must not be less than one!");
        }
        this.page = page;
    }

    /**
     * 起始位置，偏移位置
     */
    public long getOffset() {
        return (page - 1) * size;
    }

    /**
     * 是否有上一页
     */
    public boolean hasPrevious() {
        return page > 1;
    }

    /**
     * 下一页
     */
    public PageRequest next() {
        return new PageRequest(getPage() + 1, getSize());
    }

    /**
     * 上一页
     */
    public PageRequest previous() {
        return getPage() == 1 ? this : new PageRequest(getPage() - 1, getSize());
    }

    /**
     * 第一页
     */
    public PageRequest first() {
        return new PageRequest(1, getSize());
    }

    /**
     * 如果当前页已经是第一页，则返回第一页否则返回前一页
     */
    public PageRequest previousOrFirst() {
        return hasPrevious() ? previous() : first();
    }
}