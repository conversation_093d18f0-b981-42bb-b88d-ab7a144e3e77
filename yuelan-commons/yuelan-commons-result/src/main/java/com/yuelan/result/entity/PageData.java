package com.yuelan.result.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AccessLevel;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p> 分页返回 </p>
 */
@Data
@Accessors(chain = true)
@RequiredArgsConstructor(staticName = "of", access = AccessLevel.PRIVATE)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PageData<T> implements Serializable {

    private static final long serialVersionUID = -4257092979237049883L;
    /**
     * 结果集
     */
    private List<T> list;
    /**
     * 总记录数
     */
    private Long total;
    /**
     * 页码，从1开始
     */
    private Long page;
    /**
     * 每页显示数量
     */
    private Integer size;
    /**
     * 扩展信息
     */
    private Map<?, ?> extra;

    /**
     * 正常返回
     *
     * @param data 数据内容
     * @param <T>  返回数据的类型
     * @return 包装后的返回
     */
    public static <T> PageData<T> create(List<T> data) {
        return create(data, null);
    }

    public static <T> PageData<T> create(List<T> data, Long total) {
        return create(data, total, null, null);
    }

    public static <T> PageData<T> create(List<T> data, Long total, Long page, Integer size) {
        return create(data, total, page, size, null);
    }

    public static <T> PageData<T> create(List<T> data, Long total, Long page, Integer size, Map<?, ?> extra) {
        return PageData.<T>of().setList(data).setTotal(total).setPage(page).setSize(size).setExtra(extra);
    }
}