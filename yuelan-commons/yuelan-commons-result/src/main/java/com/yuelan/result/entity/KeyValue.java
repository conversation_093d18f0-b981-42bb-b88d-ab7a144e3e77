package com.yuelan.result.entity;

import com.yuelan.result.able.IEnum;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

@Data
@NoArgsConstructor
public class KeyValue<k, v> {

    private k key;

    private v value;

    public KeyValue(k key, v value) {
        if (Objects.isNull(key)) {
            throw new IllegalArgumentException("Key must not be null");
        }
        this.key = key;
        this.value = value;
    }

    public static KeyValue<Integer, String> of(IEnum IEnum) {
        if (Objects.isNull(IEnum)) {
            return null;
        }
        return new KeyValue<>(IEnum.getCode(), IEnum.getDesc());
    }
}