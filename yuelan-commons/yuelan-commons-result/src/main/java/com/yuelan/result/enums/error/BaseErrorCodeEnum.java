package com.yuelan.result.enums.error;

import com.yuelan.result.able.IErrorCode;
import lombok.AllArgsConstructor;
import lombok.Getter;


/**
 * <p> 基础错误枚举 </p>
 */
@Getter
@AllArgsConstructor
public enum BaseErrorCodeEnum implements IErrorCode<BaseErrorCodeEnum> {
    //自定义异常
    UNKNOWN_ERROR("9999", "未知错误"),

    INTERNAL_SERVER_ERROR("1000", "服务器内部异常"),
    SYSTEM_BUSY("1001", "系统繁忙，请稍候再试"),
    PARAMS_ERROR("1002", "参数错误"),
    FALLBACK("1003", "远程调用失败"),
    PASSWORD_ERROR("1004", "请输入正确的密码"),
    SYS_ERROR("1005", "系统错误"),

    UNAUTHORIZED("1100", "访问被拒绝"),
    RELOGIN("1101", "登录已失效，请您重新登录"),
    AUTH_FAIL("1102", "用户鉴权失败"),
    NOT_PERMISSION("1103", "用户无对应权限，禁止访问"),
    NOT_ROLE("1104", "用户无对应角色，禁止访问");

    //
    ;

    private String code;
    private String desc;
}
