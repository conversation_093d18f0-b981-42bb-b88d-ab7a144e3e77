package com.yuelan.result.enums;

import java.util.Locale;
import java.util.Optional;

/**
 * <p> 排序方式 </p>
 *
 * <AUTHOR>
 * @date 2023/9/22
 */
public enum DirectionEnum {
    ASC,
    DESC;

    public static DirectionEnum fromString(String value) {

        try {
            return DirectionEnum.valueOf(value.toUpperCase(Locale.US));
        } catch (Exception e) {
            throw new IllegalArgumentException(String.format(
                    "Invalid value '%s' for orders given; Has to be either 'desc' or 'asc' (case insensitive)", value), e);
        }
    }

    public static Optional<DirectionEnum> fromOptionalString(String value) {

        try {
            return Optional.of(fromString(value));
        } catch (IllegalArgumentException e) {
            return Optional.empty();
        }
    }

    public boolean isAscending() {
        return this.equals(ASC);
    }

    public boolean isDescending() {
        return this.equals(DESC);
    }
}