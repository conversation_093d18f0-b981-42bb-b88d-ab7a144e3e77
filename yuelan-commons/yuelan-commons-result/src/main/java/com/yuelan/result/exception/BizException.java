package com.yuelan.result.exception;

import com.yuelan.result.able.IErrorCode;
import com.yuelan.result.able.IException;
import com.yuelan.result.entity.BizResult;
import lombok.*;

/**
 * <p> 业务异常 </p>
 */
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Getter
@Setter(AccessLevel.PRIVATE)
public class BizException extends RuntimeException implements IException {

    @NonNull
    private String code;

    @NonNull
    @Setter
    private String msg;

    private BizException(String code, String msg) {
        super(msg);
        this.code = code;
        this.msg = msg;
    }

    private static BizException of(String code, String msg) {
        return new BizException(code, msg);
    }

    /**
     * @param error 错误码枚举
     */
    public static BizException create(@NonNull IErrorCode error) {
        return com.yuelan.result.exception.BizException.of(error.getCode(), error.getDesc());
    }

    /**
     * @param error        错误码
     * @param errorMessage 自定义错误信息
     */
    public static BizException create(@NonNull IErrorCode error, @NonNull String errorMessage) {
        if (errorMessage.isEmpty() || errorMessage.trim().isEmpty()) {
            return com.yuelan.result.exception.BizException.of(error.getCode(), error.getDesc());
        }
        return com.yuelan.result.exception.BizException.of(error.getCode(), errorMessage);
    }

    public static void assertCheck(boolean expression, @NonNull IErrorCode error, @NonNull String errorMessage) {
        if (!expression) {
            throw create(error, errorMessage);
        }
    }

    public static void assertCheck(boolean expression, @NonNull IErrorCode error) {
        if (!expression) {
            throw create(error);
        }
    }

    /**
     * @param bizResult 错误结果集
     */
    public static BizException create(@NonNull BizResult bizResult) {
        return com.yuelan.result.exception.BizException.of(bizResult.getCode(), bizResult.getMsg());
    }
}