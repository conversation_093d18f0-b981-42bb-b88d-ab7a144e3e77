package com.yuelan.result.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <p> 人民币单位 </p>
 */
@Getter
@AllArgsConstructor
public enum MoneyUnit {
    /**
     * 人民币单位
     */
    HAO("豪", 1),
    LI("厘", HAO.rate * 10),
    FEN("分", LI.rate * 10),
    JIAO("角", FEN.rate * 10),
    YUAN("元", JIAO.rate * 10),
    ;
    /**
     * 单位
     */
    private String unit;
    /**
     * 进率
     */
    private long rate;

    /**
     * 单位互转
     *
     * @param money        金额
     * @param source       原始单位
     * @param target       目标单位
     * @param scale        保留小数
     * @param roundingMode 截取模式
     */
    public static BigDecimal convert(long money, MoneyUnit source, MoneyUnit target, int scale,
                                     RoundingMode roundingMode) {
        BigDecimal rate = BigDecimal.valueOf(target.rate).divide(BigDecimal.valueOf(source.rate));
        return BigDecimal.valueOf(money).divide(rate, scale, roundingMode);
    }

    public static BigDecimal toHao(long money, MoneyUnit source, int scale, RoundingMode roundingMode) {
        return convert(money, source, MoneyUnit.HAO, scale, roundingMode);
    }

    public static BigDecimal toHao(long money, MoneyUnit source) {
        return toHao(money, source, 0, RoundingMode.DOWN);
    }

    public static BigDecimal toLi(long money, MoneyUnit source, int scale, RoundingMode roundingMode) {
        return convert(money, source, MoneyUnit.LI, scale, roundingMode);
    }

    public static BigDecimal toLi(long money, MoneyUnit source) {
        return toLi(money, source, 0, RoundingMode.DOWN);
    }

    public static BigDecimal toFen(long money, MoneyUnit source, int scale, RoundingMode roundingMode) {
        return convert(money, source, MoneyUnit.FEN, scale, roundingMode);
    }

    public static BigDecimal toFen(long money, MoneyUnit source) {
        return toFen(money, source, 0, RoundingMode.DOWN);
    }

    public static BigDecimal toJiao(long money, MoneyUnit source, int scale, RoundingMode roundingMode) {
        return convert(money, source, MoneyUnit.JIAO, scale, roundingMode);
    }

    public static BigDecimal toJiao(long money, MoneyUnit source) {
        return toJiao(money, source, 0, RoundingMode.DOWN);
    }

    public static BigDecimal toYuan(long money, MoneyUnit source, int scale, RoundingMode roundingMode) {
        return convert(money, source, MoneyUnit.YUAN, scale, roundingMode);
    }

    public static BigDecimal toYuan(long money, MoneyUnit source) {
        return toYuan(money, source, 2, RoundingMode.DOWN);
    }
}
