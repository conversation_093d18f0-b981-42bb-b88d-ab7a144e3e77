package com.yuelan.result.entity;

import com.yuelan.result.enums.YesOrNoEnum;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

@Data
public class BaseDO {

    /**
     * 创建时间
     */
    protected Date createTime;
    /**
     * 更新时间
     */
    protected Date updateTime;
    /**
     * 删除字段
     *
     * @see YesOrNoEnum
     */
    protected Integer deleted;

    /**
     * 初始化
     */
    public void init() {
        init(new Date());
    }

    /**
     * 初始化
     *
     * @param date 当前时间
     */
    public void init(Date date) {
        this.deleted = YesOrNoEnum.NO.getCode();
        this.createTime = date;
        this.updateTime = date;
    }

    /**
     * BO转DO
     */
    public void from(BaseBO baseBO) {
        if (Objects.isNull(baseBO)) {
            return;
        }
        this.deleted = YesOrNoEnum.getCode(baseBO.isDeleted());
        this.createTime = baseBO.getCreateTime();
        this.updateTime = baseBO.getUpdateTime();
    }

    /**
     * 是否删除
     */
    public Boolean hasDeleted() {
        return YesOrNoEnum.isYes(this.deleted);
    }

}