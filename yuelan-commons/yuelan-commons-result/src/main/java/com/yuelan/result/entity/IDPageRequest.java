package com.yuelan.result.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <p> ID分页对象 </p>
 */
@Data
@NoArgsConstructor
public class IDPageRequest<T> implements java.io.Serializable {

    private static final long serialVersionUID = -3249240106177676069L;
    /**
     * 分页ID
     * 上一页：第一条记录的ID
     * 下一页：最后一条记录的ID
     */
    private T pageId;
    /**
     * 每页显示数量
     */
    private int size = 10;
    /**
     * 0上一页，1下一页
     */
    private int direction = 1;

    private IDPageRequest(T pageId, int size, int direction) {
        if (size < 1) {
            throw new IllegalArgumentException("Page size must not be less than one!");
        }

        this.pageId = pageId;
        this.size = size;
        this.direction = direction;
    }

    public static <T> IDPageRequest<T> of(T pageId, int size, int direction) {
        return new IDPageRequest<T>(pageId, size, direction);
    }

    /**
     * 上一页
     */
    public static <T> IDPageRequest<T> previous(T pageId, int size) {
        return new IDPageRequest<T>(pageId, size, 0);
    }

    /**
     * 下一页
     */
    public static <T> IDPageRequest<T> next(T pageId, int size) {
        return new IDPageRequest<T>(pageId, size, 1);
    }

    /**
     * 第一页
     */
    public static <T> IDPageRequest<T> first(int size) {
        return next(null, size);
    }

    /**
     * 最后一页
     */
    public static <T> IDPageRequest<T> last(int size) {
        return previous(null, size);
    }

    /**
     * 是否下一页
     */
    public boolean hasNext() {
        return Objects.equals(direction, 1);
    }

}