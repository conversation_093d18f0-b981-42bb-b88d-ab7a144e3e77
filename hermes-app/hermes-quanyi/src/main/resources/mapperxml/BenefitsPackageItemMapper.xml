<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.BenefitsPackageItemMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitsPackageItemDO">
    <!--@mbg.generated-->
    <!--@Table benefits_package_item-->
    <id column="package_item_id" jdbcType="BIGINT" property="packageItemId" />
    <result column="package_id" jdbcType="BIGINT" property="packageId" />
    <result column="benefit_item_id" jdbcType="BIGINT" property="benefitItemId" />
    <result column="dispatch_timing" jdbcType="TINYINT" property="dispatchTiming" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  
  <resultMap id="AssociationBaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO"
             extends="com.yuelan.hermes.quanyi.mapper.BenefitItemMapper.BaseResultMap">
    <result column="dispatch_timing" jdbcType="TINYINT" property="dispatchTiming" />
  </resultMap>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    package_item_id, package_id, benefit_item_id, dispatch_timing, create_time, update_time
  </sql>
    
  <select id="listBenefitsByPackageId" resultType="com.yuelan.hermes.quanyi.common.pojo.domain.BenefitItemDO">
    SELECT i.*,
           pi.dispatch_timing
    FROM benefits_package_item pi
           LEFT JOIN benefit_item i ON pi.benefit_item_id = i.benefit_item_id
    WHERE pi.package_id = #{packageId}
    order by pi.sort
  </select>
</mapper>