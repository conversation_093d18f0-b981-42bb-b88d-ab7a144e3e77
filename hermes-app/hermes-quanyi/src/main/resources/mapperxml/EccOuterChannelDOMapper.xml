<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yuelan.hermes.quanyi.mapper.EccOuterChannelDOMapper">
  <resultMap id="BaseResultMap" type="com.yuelan.hermes.quanyi.common.pojo.domain.EccOuterChannelDO">
    <!--@mbg.generated-->
    <!--@Table ecc_outer_channel-->
    <id column="outer_channel_id" jdbcType="BIGINT" property="outerChannelId" />
    <result column="channel_name" jdbcType="VARCHAR" property="channelName" />
    <result column="api_key" jdbcType="VARCHAR" property="apiKey" />
    <result column="api_secret" jdbcType="VARCHAR" property="apiSecret" />
    <result column="is_disabled" jdbcType="INTEGER" property="isDisabled" />
    <result column="ip_whitelist" jdbcType="VARCHAR" property="ipWhitelist" />
    <result column="zop_referrer_code" jdbcType="VARCHAR" property="zopReferrerCode" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="INTEGER" property="deleted" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    outer_channel_id, channel_name, api_key, api_secret, is_disabled,ip_whitelist,zop_referrer_code, create_time,
    update_time, deleted
  </sql>
</mapper>