package com.yuelan.hermes.quanyi.controller;

import com.yuelan.hermes.quanyi.biz.service.EccIccIdService;
import com.yuelan.hermes.quanyi.controller.request.EccIccIdReq;
import com.yuelan.hermes.quanyi.controller.response.EccIccIdResp;
import com.yuelan.hermes.quanyi.controller.response.ImportResp;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.io.IOException;

/**
 * ICCID管理控制器
 *
 * <AUTHOR> Assistant
 * @since 2025-07-18
 */
@Validated
@Tag(name = "电商卡/后台api/ICCID管理")
@RestController
@RequestMapping("/a/ecc/iccid")
@AllArgsConstructor
public class EccIccIdController {

    private final EccIccIdService eccIccIdService;

    @Operation(summary = "分页查询ICCID")
    @PostMapping("/page")
    public BizResult<PageData<EccIccIdResp>> page(@RequestBody @Validated EccIccIdReq req) {
        return BizResult.create(eccIccIdService.pageIccId(req));
    }

    @Operation(summary = "下载导入模板")
    @GetMapping(value = "/download/template", produces = "application/octet-stream")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        eccIccIdService.downloadTemplate(response);
    }

    @Operation(summary = "批量导入ICCID", description = "支持Excel文件格式，必须使用官方模板")
    @Parameters({
            @Parameter(name = "channelId", description = "渠道ID", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "productId", description = "产品ID", required = true, in = ParameterIn.QUERY),
            @Parameter(name = "file", description = "文件", required = true, in = ParameterIn.QUERY)
    })
    @PostMapping(value = "/import", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public BizResult<ImportResp> importData(@RequestParam("channelId") @NotNull Long channelId,
                                            @RequestParam("channelType") @NotNull Integer channelType,
                                            @RequestParam("productId") @NotNull Long productId,
                                            @RequestParam("file") @NotNull MultipartFile file) throws IOException {
        return BizResult.create(eccIccIdService.importIccIdFile(file, channelType, channelId, productId));
    }
}
