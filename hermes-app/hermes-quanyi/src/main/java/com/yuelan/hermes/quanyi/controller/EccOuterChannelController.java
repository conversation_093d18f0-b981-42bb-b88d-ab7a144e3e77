package com.yuelan.hermes.quanyi.controller;

import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.core.validator.validate.AddGroup;
import com.yuelan.core.validator.validate.EditGroup;
import com.yuelan.hermes.quanyi.biz.service.EccOuterChannelDOService;
import com.yuelan.hermes.quanyi.config.satoken.StpAdminUtil;
import com.yuelan.hermes.quanyi.controller.request.EccOuterChannelListReq;
import com.yuelan.hermes.quanyi.controller.request.EccOuterChannelSaveReq;
import com.yuelan.hermes.quanyi.controller.response.EccOuterChannelResp;
import com.yuelan.plugins.log.annotation.Log;
import com.yuelan.plugins.log.enums.OperationType;
import com.yuelan.result.entity.BizResult;
import com.yuelan.result.entity.PageData;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> 2024/5/17 下午4:03
 */
@Validated
@RestController
@Tag(name = "电商卡/后台api/外部渠道管理")
@RequiredArgsConstructor
@RequestMapping("/a/ecc/outerChannel")
public class EccOuterChannelController {

    private final EccOuterChannelDOService eccOuterChannelDOService;

    public static void main(String[] args) {
        String secret = "dXolDt15gxd3g3XbVV4VPv6W4rjDR3ya";
        JSONObject J = JSONObject.parseObject("{\n" +
                "    \"zopGoodsId\": \"344205081242\",\n" +
                "    \"postProvinceCode\": \"330000\",\n" +
                "    \"address\": \"测试请勿发卡\",\n" +
                "    \"channelOrderNo\": \"12t78eggsdkfg\",\n" +
                "    \"contactPhone\": \"13989458839\",\n" +
                "    \"eccProdCode\": \"abcabc\",\n" +
                "    \"idCardName\": \"王某某某\",\n" +
                "    \"idCardNo\": \"340222199110286619\",\n" +
                "    \"postCityCode\": \"330100\",\n" +
                "    \"postDistrictCode\": \"330106\",\n" +
                "    \"pageUrl\": \"http://umjbq.sa/rmhng\"\n" +
                "}");
        StringBuilder stringBuilder = new StringBuilder();
        // 字典排序
        List<String> keys = new ArrayList<>(J.keySet());
        keys.sort(String::compareTo);
        Collections.sort(keys);
        for (String key : keys) {
            Object value = J.get(key);
            if (Objects.nonNull(value)) {
                stringBuilder.append(key).append("=").append(value).append("&");
            }
        }
        stringBuilder.append("secret=").append(secret);
        System.out.println(stringBuilder.toString());
        System.out.println(SecureUtil.md5(stringBuilder.toString()));
    }

    /**
     * 新增外部渠道
     */
    @Log(title = "新增电商卡外部渠道", type = OperationType.INSERT)
    @Operation(summary = "新增电商卡外部渠道")
    @PostMapping("/add")
    public BizResult<Void> add(@RequestBody @Validated(AddGroup.class) EccOuterChannelSaveReq req) {
        eccOuterChannelDOService.save(req);
        return BizResult.ok();
    }

    /**
     * 更新外部渠道
     */
    @Log(title = "更新电商卡外部渠道", type = OperationType.UPDATE)
    @Operation(summary = "更新电商卡外部渠道")
    @PostMapping("/edit")
    public BizResult<Void> edit(@RequestBody @Validated(EditGroup.class) EccOuterChannelSaveReq req) {
        eccOuterChannelDOService.update(req);
        return BizResult.ok();
    }

    /**
     * 电商卡外部渠道分页列表
     */
    @Operation(summary = "电商卡外部渠道分页列表")
    @PostMapping("/page")
    public BizResult<PageData<EccOuterChannelResp>> page(@RequestBody EccOuterChannelListReq req) {
        return BizResult.create(eccOuterChannelDOService.pageList(req));
    }


    @Operation(summary = "电商卡外部渠道选择项")
    @PostMapping("/selectOptions")
    public BizResult<List<EccOuterChannelResp>> selectOptions() {
        // 不同账号看见的不一样
        long adminId = StpAdminUtil.getLoginIdAsLong();
        return BizResult.create(eccOuterChannelDOService.selectOptionsByAdmin(adminId));
    }
}
