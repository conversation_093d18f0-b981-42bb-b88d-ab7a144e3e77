// package com.yuelan.hermes.quanyi.util;
//
// import cn.hutool.core.date.DatePattern;
// import cn.hutool.core.date.LocalDateTimeUtil;
// import com.yuelan.hermes.quanyi.remote.JinXiManager;
// import com.yuelan.hermes.quanyi.remote.jinxi.request.JinXiOrderRechargeReq;
// import lombok.extern.slf4j.Slf4j;
// import org.junit.jupiter.api.Test;
//
// import java.time.LocalDateTime;
//
// /**
//  * 今溪工具类测试
//  *
//  * <AUTHOR> 2025/8/2
//  * @since 2025/8/2
//  */
// @Slf4j
// public class JinXiUtilTest {
//
//     @Test
//     void testSignGeneration() {
//         log.info("=== 今溪签名生成测试 ===");
//
//         // 创建测试请求
//         JinXiOrderRechargeReq req = new JinXiOrderRechargeReq();
//         req.setAppKey("test_app_key");
//         req.setBuyNumber(1);
//         req.setCustomOrderId("TEST123456");
//         req.setProductId("PROD001");
//         req.setTimestamp("2025-08-02 10:30:00");
//         req.setVersion("2.0");
//         req.setSignType("md5");
//         req.setRechargeAccount("***********");
//
//         String signKey = "test_sign_key";
//
//         // 生成签名
//         String sign = JinXiManager.generateOrderRechargeSign(req, signKey);
//
//         log.info("生成的签名: {}", sign);
//
//         // 手动验证签名生成过程
//         log.info("=== 手动验证签名生成过程 ===");
//         log.info("1. 参数排序后: app_key=test_app_key, buy_number=1, custom_order_id=TEST123456, product_id=PROD001, recharge_account=***********, sign_type=md5, timestamp=2025-08-02 10:30:00, version=2.0");
//         log.info("2. 参数串联: app_keytest_app_keybuy_number1custom_order_idTEST123456product_idPROD001recharge_account***********sign_typemd5timestamp2025-08-02 10:30:00version2.0");
//         log.info("3. 前后拼接密钥: test_sign_key + 参数串 + test_sign_key");
//         log.info("4. MD5加密并转大写");
//
//         // 验证签名长度
//         assert sign.length() == 32 : "签名长度应该是32位";
//         assert sign.equals(sign.toUpperCase()) : "签名应该是大写";
//
//         log.info("✅ 签名生成测试通过");
//     }
//
//     @Test
//     void testTimestampGeneration() {
//         String timestamp  = LocalDateTimeUtil.format(LocalDateTime.now(), DatePattern.NORM_DATETIME_PATTERN);
//         log.info("生成的时间戳: {}", timestamp);
//
//         // 验证时间戳格式
//         assert timestamp.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}") : "时间戳格式不正确";
//
//         log.info("✅ 时间戳生成测试通过");
//     }
// }
