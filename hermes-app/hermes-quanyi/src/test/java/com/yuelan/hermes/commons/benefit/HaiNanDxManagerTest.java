package com.yuelan.hermes.commons.benefit;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSONObject;
import com.yuelan.hermes.quanyi.QuanYiApplication;
import com.yuelan.hermes.quanyi.biz.handler.impl.BenefitHaiNanDxPayChannel;
import com.yuelan.hermes.quanyi.common.enums.HNDxPayPkgEnum;
import com.yuelan.hermes.quanyi.common.pojo.bo.BenefitPayResultBO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitOrderDO;
import com.yuelan.hermes.quanyi.common.pojo.domain.BenefitProductDO;
import com.yuelan.hermes.quanyi.controller.request.UserBenefitOrderReq;
import com.yuelan.hermes.quanyi.remote.benefit.HaiNanDxManager;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.net.HttpCookie;

/**
 * <AUTHOR> 2025/3/26
 * @since 2025/3/26
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = {QuanYiApplication.class}, properties = {"spring.profiles.active=local"})
public class HaiNanDxManagerTest {

    @Resource
    private HaiNanDxManager haiNanDxManager;
    HNDxPayPkgEnum pkg = HNDxPayPkgEnum.HN_PKG_29_YEAR;
    String mobile = "19030925187";
    @Resource
    private BenefitHaiNanDxPayChannel benefitHaiNanDxPayChannel;

    @Test
    public void testSendSmsCode() throws InterruptedException {


        UserBenefitOrderReq req = new UserBenefitOrderReq();
        req.setMobile(mobile);

        BenefitProductDO productDO = new BenefitProductDO();
        productDO.setPayChannelPkgId(pkg.getPkgId());

        boolean b = benefitHaiNanDxPayChannel.orderSendSmsCode(req, productDO);
        System.out.println("发送短信验证码结果: " + b);
    }

    @Test
    public void verifySmsCode() {
        UserBenefitOrderReq req = new UserBenefitOrderReq();
        req.setMobile(mobile);
        req.setSmsCode("772826");

        BenefitProductDO productDO = new BenefitProductDO();
        productDO.setPayChannelPkgId(pkg.getPkgId());

        BenefitOrderDO orderDO = new BenefitOrderDO();
        BenefitPayResultBO resultBO = benefitHaiNanDxPayChannel.getPayUrl(req, productDO, orderDO);
        System.out.println("获取支付链接结果: " + JSONObject.toJSONString(resultBO));

        System.out.println(orderDO.getOrderNo());


    }

    @Test
    public void cookieTest() {
        HttpRequest post = HttpRequest.post("zhihu.com");
        for (HttpCookie cookie : post.execute().getCookies()) {
            String cookieStr = JSONObject.toJSONString(cookie);
            System.out.println(cookieStr);

        }
    }

}
